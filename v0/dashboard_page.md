"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Bell,
  Calendar,
  Download,
  LogOut,
  Menu,
  MessageSquare,
  RefreshCw,
  Search,
  TrendingUp,
  Heart,
  Lightbulb,
  DollarSign,
  Zap,
  Target,
} from "lucide-react"
import {
  Area,
  AreaChart as RechartsAreaChart,
  Bar,
  BarChart as RechartsBarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart as Recharts<PERSON>ine<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
} from "recharts"

// Mock data for charts
const financialData = [
  { month: "Jan", revenue: 42.5, expenses: 32.8, profit: 9.7 },
  { month: "Feb", revenue: 44.3, expenses: 33.1, profit: 11.2 },
  { month: "Mar", revenue: 46.8, expenses: 34.5, profit: 12.3 },
  { month: "Apr", revenue: 48.2, expenses: 35.2, profit: 13.0 },
  { month: "May", revenue: 50.5, expenses: 36.8, profit: 13.7 },
  { month: "Jun", revenue: 52.1, expenses: 37.5, profit: 14.6 },
  { month: "Jul", revenue: 54.8, expenses: 38.2, profit: 16.6 },
  { month: "Aug", revenue: 56.3, expenses: 39.1, profit: 17.2 },
  { month: "Sep", revenue: 58.7, expenses: 40.5, profit: 18.2 },
  { month: "Oct", revenue: 60.2, expenses: 41.8, profit: 18.4 },
  { month: "Nov", revenue: 62.5, expenses: 42.3, profit: 20.2 },
  { month: "Dec", revenue: 65.8, expenses: 43.5, profit: 22.3 },
]

const operationalData = [
  { month: "Jan", claimProcessing: 4.2, underwritingAccuracy: 92, slaCompliance: 88 },
  { month: "Feb", claimProcessing: 4.0, underwritingAccuracy: 93, slaCompliance: 89 },
  { month: "Mar", claimProcessing: 3.8, underwritingAccuracy: 94, slaCompliance: 90 },
  { month: "Apr", claimProcessing: 3.5, underwritingAccuracy: 94, slaCompliance: 91 },
  { month: "May", claimProcessing: 3.3, underwritingAccuracy: 95, slaCompliance: 92 },
  { month: "Jun", claimProcessing: 3.2, underwritingAccuracy: 95, slaCompliance: 93 },
]

const customerData = [
  { month: "Jan", satisfaction: 4.2, nps: 42, retention: 88 },
  { month: "Feb", satisfaction: 4.3, nps: 45, retention: 89 },
  { month: "Mar", satisfaction: 4.4, nps: 48, retention: 90 },
  { month: "Apr", satisfaction: 4.5, nps: 52, retention: 91 },
  { month: "May", satisfaction: 4.6, nps: 56, retention: 92 },
  { month: "Jun", satisfaction: 4.7, nps: 58, retention: 93 },
]

const innovationData = [
  { month: "Jan", newProducts: 2, digitalAdoption: 45, marketShare: 16.2 },
  { month: "Feb", newProducts: 2, digitalAdoption: 48, marketShare: 16.5 },
  { month: "Mar", newProducts: 3, digitalAdoption: 52, marketShare: 16.8 },
  { month: "Apr", newProducts: 3, digitalAdoption: 56, marketShare: 17.2 },
  { month: "May", newProducts: 4, digitalAdoption: 62, marketShare: 17.8 },
  { month: "Jun", newProducts: 5, digitalAdoption: 68, marketShare: 18.5 },
]

const radarData = [
  { subject: "Financial", A: 85, fullMark: 100 },
  { subject: "Operational", A: 92, fullMark: 100 },
  { subject: "Customer", A: 88, fullMark: 100 },
  { subject: "Innovation", A: 78, fullMark: 100 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"]

export default function CEODashboardPage() {
  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="icon" className="md:hidden">
              <Menu className="h-5 w-5" />
            </Button>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search dashboard..."
                className="pl-8 pr-4 py-2 w-full border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
            </Button>
            <Button variant="ghost" size="icon">
              <MessageSquare className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <LogOut className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="flex-1 overflow-y-auto p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">CEO 4 Aspect Dashboard</h1>
            <p className="text-gray-600">Executive-level monitoring of key business aspects</p>
          </div>
          <div className="flex items-center gap-3 mt-4 md:mt-0">
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Date Range
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Overall Performance Card */}
        <div className="mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Overall Business Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                <div className="col-span-1 md:col-span-2">
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="subject" />
                        <PolarRadiusAxis angle={30} domain={[0, 100]} />
                        <Radar name="Performance" dataKey="A" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                        <Legend />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <div className="col-span-1 md:col-span-3">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                    <div className="flex items-start gap-3">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <span className="text-blue-800 font-bold">AI</span>
                      </div>
                      <div>
                        <p className="text-sm text-blue-800 mb-2">
                          <span className="font-medium">Executive Summary:</span> Based on the 4 key aspects, here are
                          the highlights:
                        </p>
                        <div className="space-y-2 text-sm text-gray-700">
                          <ul className="list-disc pl-5 space-y-1">
                            <li>
                              <span className="font-medium">Financial Performance:</span> Revenue growth is strong at
                              15.3% YoY with improving profit margins (34.2%).
                            </li>
                            <li>
                              <span className="font-medium">Operational Excellence:</span> Claims processing time has
                              improved by 28.9% to 3.2 days, with underwriting accuracy at 95%.
                            </li>
                            <li>
                              <span className="font-medium">Customer Experience:</span> NPS has increased to 58 (from 42
                              in January), and customer satisfaction is at an all-time high of 4.7/5.
                            </li>
                            <li>
                              <span className="font-medium">Innovation & Growth:</span> Digital adoption has increased
                              to 68% and market share has grown to 18.5%.
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg bg-green-50">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-5 w-5 text-green-600" />
                        <h3 className="font-semibold text-green-800">Top Performer</h3>
                      </div>
                      <p className="text-sm text-gray-700">
                        Operational Excellence has shown the most improvement with a 28.9% reduction in claims
                        processing time.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg bg-amber-50">
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="h-5 w-5 text-amber-600" />
                        <h3 className="font-semibold text-amber-800">Focus Area</h3>
                      </div>
                      <p className="text-sm text-gray-700">
                        Innovation & Growth requires attention with new product adoption rates below target by 12%.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 4 Aspect Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Financial Performance */}
          <Card className="col-span-1">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg font-semibold">Financial Performance</CardTitle>
                </div>
                <div className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                  Score: 85/100
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Revenue Growth</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">15.3%</div>
                    <div className="text-green-500 text-sm">+2.1% YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Profit Margin</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">34.2%</div>
                    <div className="text-green-500 text-sm">+1.8% YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Premium Collection</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">96.5%</div>
                    <div className="text-green-500 text-sm">+0.8% YoY</div>
                  </div>
                </div>
              </div>
              <div className="h-60">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsAreaChart
                    data={financialData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name="Revenue (M฿)"
                    />
                    <Area
                      type="monotone"
                      dataKey="expenses"
                      stackId="2"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name="Expenses (M฿)"
                    />
                    <Area
                      type="monotone"
                      dataKey="profit"
                      stackId="3"
                      stroke="#ffc658"
                      fill="#ffc658"
                      name="Profit (M฿)"
                    />
                  </RechartsAreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Operational Excellence */}
          <Card className="col-span-1">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <Zap className="h-4 w-4 text-green-600" />
                  </div>
                  <CardTitle className="text-lg font-semibold">Operational Excellence</CardTitle>
                </div>
                <div className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                  Score: 92/100
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Claims Processing</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">3.2 days</div>
                    <div className="text-green-500 text-sm">-28.9% YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Underwriting Accuracy</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">95%</div>
                    <div className="text-green-500 text-sm">+2.1% YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">SLA Compliance</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">93%</div>
                    <div className="text-green-500 text-sm">+5.7% YoY</div>
                  </div>
                </div>
              </div>
              <div className="h-60">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={operationalData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" domain={[80, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="claimProcessing"
                      stroke="#8884d8"
                      name="Claims Processing (days)"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="underwritingAccuracy"
                      stroke="#82ca9d"
                      name="Underwriting Accuracy (%)"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="slaCompliance"
                      stroke="#ffc658"
                      name="SLA Compliance (%)"
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Customer Experience */}
          <Card className="col-span-1">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <Heart className="h-4 w-4 text-purple-600" />
                  </div>
                  <CardTitle className="text-lg font-semibold">Customer Experience</CardTitle>
                </div>
                <div className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">
                  Score: 88/100
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Satisfaction Score</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">4.7/5.0</div>
                    <div className="text-green-500 text-sm">+0.5 YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Net Promoter Score</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">58</div>
                    <div className="text-green-500 text-sm">+16 YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Retention Rate</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">93%</div>
                    <div className="text-green-500 text-sm">+5% YoY</div>
                  </div>
                </div>
              </div>
              <div className="h-60">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={customerData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" domain={[0, 5]} />
                    <YAxis yAxisId="right" orientation="right" domain={[0, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="satisfaction"
                      stroke="#8884d8"
                      name="Satisfaction Score (1-5)"
                    />
                    <Line yAxisId="right" type="monotone" dataKey="nps" stroke="#82ca9d" name="Net Promoter Score" />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="retention"
                      stroke="#ffc658"
                      name="Retention Rate (%)"
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Innovation & Growth */}
          <Card className="col-span-1">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                    <Lightbulb className="h-4 w-4 text-amber-600" />
                  </div>
                  <CardTitle className="text-lg font-semibold">Innovation & Growth</CardTitle>
                </div>
                <div className="px-2 py-1 bg-amber-100 text-amber-800 rounded-full text-xs font-medium">
                  Score: 78/100
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">New Products</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">5</div>
                    <div className="text-green-500 text-sm">+3 YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Digital Adoption</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">68%</div>
                    <div className="text-green-500 text-sm">+23% YoY</div>
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm text-gray-500 mb-1">Market Share</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold">18.5%</div>
                    <div className="text-green-500 text-sm">+2.3% YoY</div>
                  </div>
                </div>
              </div>
              <div className="h-60">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={innovationData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" domain={[0, 100]} />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="newProducts" fill="#8884d8" name="New Products" />
                    <Bar yAxisId="right" dataKey="digitalAdoption" fill="#82ca9d" name="Digital Adoption (%)" />
                    <Bar yAxisId="left" dataKey="marketShare" fill="#ffc658" name="Market Share (%)" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Strategic Recommendations */}
        <div className="mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Strategic Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg bg-blue-50">
                <div className="flex items-start gap-3">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                    <span className="text-blue-800 font-bold">AI</span>
                  </div>
                  <div>
                    <p className="text-sm text-blue-800 mb-2">
                      <span className="font-medium">Strategic Analysis:</span> Based on the 4 aspect performance, here
                      are key recommendations:
                    </p>
                    <div className="space-y-4 text-sm text-gray-700">
                      <div className="flex items-start gap-2">
                        <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-blue-800 font-bold text-xs">1</span>
                        </div>
                        <div>
                          <p className="font-medium">Accelerate digital transformation initiatives</p>
                          <p className="text-xs text-gray-600">
                            While digital adoption has increased to 68%, it's still below the industry benchmark of 75%.
                            Prioritize mobile app enhancements and AI-powered customer service tools.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-blue-800 font-bold text-xs">2</span>
                        </div>
                        <div>
                          <p className="font-medium">Optimize operational efficiency in regional offices</p>
                          <p className="text-xs text-gray-600">
                            While overall claims processing time has improved significantly, regional offices are still
                            1.2 days slower than headquarters. Implement standardized workflows across all locations.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-blue-800 font-bold text-xs">3</span>
                        </div>
                        <div>
                          <p className="font-medium">Expand cross-selling initiatives</p>
                          <p className="text-xs text-gray-600">
                            Current cross-selling rate is 22% compared to industry best practice of 35%. Implement
                            AI-powered recommendation engine to identify opportunities within existing customer base.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-blue-800 font-bold text-xs">4</span>
                        </div>
                        <div>
                          <p className="font-medium">Enhance new product adoption</p>
                          <p className="text-xs text-gray-600">
                            New product adoption rate is 12% below target. Develop comprehensive training programs for
                            agents and create targeted marketing campaigns to increase awareness.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Competitive Benchmarking */}
        <div className="mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Competitive Benchmarking</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-500">Metric</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500">BVTPA</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500">Industry Avg</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500">Top Performer</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500">Gap Analysis</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium">Revenue Growth</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-green-600 font-medium">15.3%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>9.8%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>18.2%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2.5">
                            <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "85%" }}></div>
                          </div>
                          <span className="ml-2 text-xs text-gray-500">85%</span>
                        </div>
                      </td>
                    </tr>
                    <tr className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium">Claims Processing Time</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-green-600 font-medium">3.2 days</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>4.5 days</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>2.8 days</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2.5">
                            <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "92%" }}></div>
                          </div>
                          <span className="ml-2 text-xs text-gray-500">92%</span>
                        </div>
                      </td>
                    </tr>
                    <tr className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium">Customer Satisfaction</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-green-600 font-medium">4.7/5.0</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>4.2/5.0</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>4.8/5.0</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2.5">
                            <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "88%" }}></div>
                          </div>
                          <span className="ml-2 text-xs text-gray-500">88%</span>
                        </div>
                      </td>
                    </tr>
                    <tr className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium">Digital Adoption</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-amber-600 font-medium">68%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>62%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>85%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2.5">
                            <div className="bg-amber-600 h-2.5 rounded-full" style={{ width: "75%" }}></div>
                          </div>
                          <span className="ml-2 text-xs text-gray-500">75%</span>
                        </div>
                      </td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium">Market Share</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-green-600 font-medium">18.5%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>12.3%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>22.7%</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2.5">
                            <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "82%" }}></div>
                          </div>
                          <span className="ml-2 text-xs text-gray-500">82%</span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
